services:
  backend:
    build:
      context: ./backend
    container_name: admin-service-backend
    ports:
      - "8003:8000"
    env_file:
      - ./backend/.env.dev
    volumes:
      - ./backend:/app
    
    depends_on:
      db:
        condition: service_healthy
    networks:
      - quanthea-corp-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://0.0.0.0:8000/health"]
      interval: 5s
      timeout: 5s
      retries: 5

  frontend:
    build:
      context: ./frontend
    container_name: admin-service-frontend
    ports:
      - "5173:5173"
    environment:
      - VITE_AUTH_SERVICE_URL=http://localhost:8002
      - VITE_ADMIN_SERVICE_URL=http://localhost:8003
    volumes:
      - ./frontend:/app
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - quanthea-corp-network

  db:
    image: postgres:16-alpine
    container_name: admin-service-db
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=pass
      - POSTGRES_DB=admin_service
    volumes:
      - admin_service_db_data:/var/lib/postgresql/data
    networks:
      - quanthea-corp-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $POSTGRES_USER -d $POSTGRES_DB"]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  admin_service_db_data:

networks:
  quanthea-corp-network:
    name: quanthea-corp-network
    external: true
