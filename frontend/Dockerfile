# Stage 1: Build the application
FROM node:20-alpine AS build

WORKDIR /app

# Copy package files and install dependencies
COPY package*.json ./
RUN npm install

# Copy the rest of the application source code
COPY . .

# Build the application
RUN npm run build

# Stage 2: Serve the application
FROM node:20-alpine

WORKDIR /app

# Install serve
RUN npm install -g serve

# Copy the build output from the build stage
COPY --from=build /app/dist .

# Expose the port the app runs on
EXPOSE 5173

# Serve the application
CMD ["serve", "-s", ".", "-l", "5173"]
