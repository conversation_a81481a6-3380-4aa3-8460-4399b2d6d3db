import React from "react";
import { Navigate } from "react-router-dom";
import tokenManager from "../utils/tokenManager";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  // Usar Token Manager para verificar token válido (inclui verificação de expiração)
  const hasValidToken = tokenManager.hasValidToken();

  if (!hasValidToken) {
    // Token inválido, expirado ou inexistente - redirecionar para login
    console.log("🔒 Acesso negado: token inválido ou expirado");
    return <Navigate to="/login" replace />;
  }

  // Token válido - permitir acesso
  return <>{children}</>;
};

export default ProtectedRoute;
