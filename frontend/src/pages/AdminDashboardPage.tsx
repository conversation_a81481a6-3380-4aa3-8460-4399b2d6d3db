import React, { useState, useEffect } from 'react';
import api from '../api/axios';
import axios from 'axios';
import { useNotification } from '../context/NotificationContext';
import DashboardCard from '../components/dashboard/DashboardCard';

interface TenantAdminResponse {
  id: string;
  name: string;
  description: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  has_api_key: boolean;
}

const AdminDashboardPage: React.FC = () => {
  const [tenants, setTenants] = useState<TenantAdminResponse[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { addNotification } = useNotification();

  useEffect(() => {
    const fetchTenants = async () => {
      try {
        setLoading(true);
        const response = await api.get<TenantAdminResponse[]>('/api/v1/admin/tenants');
        setTenants(response.data);
      } catch (err) {
        if (axios.isAxiosError(err) && err.response) {
          setError(err.response.data.detail || 'Failed to fetch tenants');
          addNotification(err.response.data.detail || 'Failed to fetch tenants', 'error');
        } else {
          setError('An unexpected error occurred');
          addNotification('An unexpected error occurred', 'error');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchTenants();
  }, []);

  const totalTenants = tenants.length;
  const activeTenants = tenants.filter(tenant => tenant.is_active).length;

  if (loading) {
    return <div className="p-4 text-gray-800">Loading dashboard data...</div>;
  }

  if (error) {
    return <div className="p-4 text-red-500">Error: {error}</div>;
  }

  return (
    <div className="p-4 bg-gray-100 min-h-full">
      <h2 className="text-3xl font-bold mb-6 text-gray-800">Admin Dashboard</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
        <DashboardCard title="Overview">
          <div className="text-gray-700">
            <p className="mb-2">Total Tenants: <span className="font-bold">{totalTenants}</span></p>
            <p className="mb-2">Active Tenants: <span className="font-bold">{activeTenants}</span></p>
            <p className="mb-2">Total Users: <span className="font-bold">[Data from Users API]</span></p>
            <p className="mb-2">Pending Approvals: <span className="font-bold">[Data from Approvals API]</span></p>
          </div>
        </DashboardCard>

        <DashboardCard title="Recent Activities">
          <ul className="list-disc list-inside text-gray-700">
            <li className="mb-1">[Activity 1: New user registered]</li>
            <li className="mb-1">[Activity 2: API Key updated for Tenant X]</li>
            <li className="mb-1">[Activity 3: Tenant Y deactivated]</li>
            <li className="mb-1">[Activity 4: System health check passed]</li>
          </ul>
        </DashboardCard>

        <DashboardCard title="Quick Stats">
          <div className="text-gray-700">
            <p className="mb-2">API Calls Today: <span className="font-bold">[Data from Metrics API]</span></p>
            <p className="mb-2">Storage Usage: <span className="font-bold">[Data from Storage API]</span></p>
            <p className="mb-2">System Errors: <span className="font-bold">[Data from Monitoring API]</span></p>
          </div>
        </DashboardCard>
      </div>

      {/* Adicione mais seções aqui conforme necessário */}

    </div>
  );
};

export default AdminDashboardPage;
