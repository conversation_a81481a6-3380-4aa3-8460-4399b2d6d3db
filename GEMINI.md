# Contexto Específico do admin-service

Este documento fornece um contexto detalhado sobre o `admin-service`, seu estado atual, funcionalidades implementadas e as principais áreas que precisam ser alinhadas com as diretrizes da Quanthea Corp e as issues criadas no Linear.

## 1. Papel e Responsabilidades Atuais

O `admin-service` é o **painel de administração e observabilidade** do ecossistema Quanthea Corp. Suas responsabilidades atuais incluem:

*   **Autenticação de Administradores:** Permite o login de usuários administradores (consumindo o `auth-service`).
*   **Gerenciamento de Tenants:** Lista tenants e permite a gestão de API Keys (criação, atualização, revogação) para esses tenants, interagindo com o `auth-service`.
*   **Dashboard Administrativo:** Exibe um dashboard básico com informações de visão geral e placeholders para métricas futuras.
*   **Navegação:** Fornece uma interface de usuário para navegar entre as diferentes seções do painel.
*   **Notificações:** Implementa um sistema de notificação para feedback ao usuário.

## 2. Tecnologias Utilizadas (Implementação Atual)

*   **Framework UI:** React
*   **Build Tool:** Vite
*   **Linguagem:** TypeScript
*   **Estilização:** Tailwind CSS
*   **Roteamento:** React Router DOM
*   **Requisições HTTP:** Axios
*   **Gerenciamento de Estado:** React Hooks (useState, useEffect, useContext, useCallback)

## 3. Componentes e Páginas Principais

*   `App.tsx`: Componente raiz que configura o layout e as rotas.
*   `LoginPage.tsx`: Página de login para administradores.
*   `TenantsPage.tsx`: Página para listar e gerenciar tenants e suas API Keys.
*   `AdminDashboardPage.tsx`: Página do dashboard com estatísticas e atividades (atualmente com placeholders).
*   `ProtectedRoute.tsx`: Componente para proteger rotas que exigem autenticação.
*   `NotificationContext.tsx`: Contexto e provedor para exibir notificações ao usuário.
*   `axios.ts`: Configuração da instância do Axios com interceptor para JWT.

## 4. Principais Endpoints Consumidos (do auth-service)

*   `POST /api/v1/admin/login`
*   `GET /api/v1/admin/tenants`
*   `POST /api/v1/admin/tenants/{tenant_id}/api-key`
*   `PUT /api/v1/admin/tenants/{tenant_id}/api-key`
*   `DELETE /api/v1/admin/tenants/{tenant_id}/api-key`

## 5. Discrepâncias e Próximos Passos (Alinhamento com QUANTHEACORP.md e Issues no Linear)

As seguintes áreas são cruciais para alinhar o `admin-service` com as especificações da holding e as melhorias planejadas (já traduzidas em issues no Linear):

*   **Segurança - Armazenamento de Token:** O token de autenticação (`admin_token`) é armazenado no `localStorage`, o que o torna vulnerável a ataques XSS. **Prioridade Alta.**
*   **Validação de Entrada de API Key:** A validação do formato da API Key no frontend é básica (`minLength`). **Prioridade Média.**
*   **Mensagens de Erro Genéricas:** As mensagens de erro exibidas ao usuário são frequentemente genéricas. **Prioridade Média.**
*   **Dados Incompletos no Dashboard:** O dashboard (`AdminDashboardPage.tsx`) contém placeholders para dados que deveriam vir de outras APIs (usuários, aprovações, métricas, etc.). **Prioridade Média.**
*   **URL Base da API Hardcoded:** A `API_BASE_URL` em `src/api/axios.ts` está hardcoded. **Prioridade Média.**
*   **Configuração ESLint:** O `eslint.config.js` pode ser configurado para regras mais estritas, conforme sugerido no `README.md`. **Prioridade Baixa.**
*   **UX - Geração de API Keys:** A UX para gerenciamento de API Keys pode ser melhorada se o backend gerar as chaves e o frontend apenas as exibir para cópia única. **Prioridade Baixa.**

## 6. Considerações Adicionais

*   **Testes:** A cobertura de testes precisa ser expandida significativamente, conforme diretriz geral do projeto.
*   **Tipagem:** A tipagem forte em TypeScript deve ser mantida e aprimorada.
*   **Idioma:** O código e os nomes de arquivos devem seguir o padrão de inglês.
*   **Nomes Semânticos:** Manter a clareza e semântica nos nomes de componentes, variáveis e funções.
