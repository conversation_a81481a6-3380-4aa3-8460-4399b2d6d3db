**Holding e Ecossistema de Aplicações — Especificação para Code Agent**

## 1. Visão Geral do Ecossistema

Este documento descreve a **holding** (plano de controle corporativo) e suas **aplicações filhas**, incluindo o `auth-service`, o `admin-service` e múltiplos **apps consumidores** (App1, App2, App3…). O objetivo é fornecer contexto e contratos de API padronizados, segurança e fluxos de comunicação, para que um code agent possa gerar implementações consistentes e compatíveis.

### 1.1. Contexto da Holding

* A **holding** é a entidade que detém e gerencia todas as aplicações filhas.
* Centraliza:

  * Governança de segurança e identidade (`auth-service`).
  * Painel de controle e observabilidade (`admin-service`).
  * Documentação de contratos e integrações.

### 1.2. Aplicações Filhas

* **auth-service**: único serviço autorizado a autenticar usuários e emitir/validar JWTs.
* **admin-service**: painel de administração único, com 1 usuário (você, o superadmin), gerencia tenants, quotas, API Keys e coleta métricas. **Ele não possui autenticação própria, apenas consome tokens emitidos pelo auth-service**.
* **AppN**: qualquer aplicação de negócio (ex: App1, App2, App3), gerencia seus próprios usuários finais e lógica de domínio.

## 2. Serviços e Responsabilidades

| Serviço       | Papel                      | Responsabilidades Principais                                                                                                                                                                                             |
| ------------- | -------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| auth-service  | Autoridade de identidade   | - Login/Logout<br>- Emissão de JWT<br>- Hash de senhas<br>- Validação e introspecção de tokens<br>- Armazena os dados de `users`, `admin_users` e `tenants`                                                              |
| admin-service | Controle e Observabilidade | - Coleta de métricas (`/admin/stats`, `/admin/health`, `/admin/logs`)<br>- Visualiza e monitora tenants (mas não os armazena)<br>- Emissão/revogação de API Keys<br>- Painel de controle para escalabilidade e auditoria |
| AppN          | Aplicação de Negócio       | - Gerenciar usuários finais<br>- Expor APIs de negócios<br>- Implementar contratos de observabilidade para o admin-service                                                                                               |

## 3. Contratos de API de Observabilidade

Todas as aplicações filhas devem implementar, no mínimo, estes endpoints:

### 3.1. **GET /admin/stats**

* **Descrição**: Dados agregados de uso e performance.
* **Response** (200):

  ```json
  {
    "tenant_id": "<app-id>",
    "total_users": <int>,
    "active_users_last_30d": <int>,
    "database_size_mb": <float>,
    "avg_response_time_ms": <float>,
    "error_rate_percent": <float>
  }
  ```

### 3.2. **GET /admin/health**

* **Descrição**: Indica se o serviço está operacional.
* **Response** (200):

  ```json
  {
    "status": "ok",           
    "uptime_seconds": <int>,
    "db_status": "connected",
    "version": "<semver>"
  }
  ```

### 3.3. **GET /admin/logs**

* **Descrição**: Eventos recentes para auditoria leve.
* **Response** (200):

  ```json
  [
    {"timestamp":"<ISO8601>","event":"<string>","details":{...}},
    ...
  ]
  ```

### 3.4. **Segurança dessas rotas**

* Autenticação: **API Key interna** ou **JWT com claim `sub=admin-service`**.
* Acesso restrito à rede interna ou a clientes autenticados especificamente.

## 4. Fluxos de Comunicação

```mermaid
flowchart TD
    subgraph Usuarios
      U1[Usuario App1] -->|Login| AUTH[auth-service]
      U2[Usuario App2] -->|Login| AUTH
      U3[Usuario App3] -->|Login| AUTH
      ADM[Superadmin] -->|Login| AUTH
    end
    subgraph Apps
      A1[App1] -->|Requisição com JWT| AUTH
      A2[App2] -->|Requisição com JWT| AUTH
      A3[App3] -->|Requisição com JWT| AUTH
      ADM -->|Requisição com JWT| ADMIN[admin-service]
    end
    subgraph Observabilidade
      ADMIN -->|GET /admin/stats<BR>/health<BR>/logs| A1
      ADMIN -->|GET /admin/...| A2
      ADMIN -->|GET /admin/...| A3
      ADMIN -->|GET /admin/...| AUTH
    end
```

## 5. Segurança e Governança

* **Rede Interna**: Serviços se comunicam em rede isolada (Docker network, VPC).
* **TLS/HTTPS** em todas as comunicações.
* **API Gateway** (opcional) para roteamento, rate limiting e autenticação unificada.
* **Logs**: Centralizados (ex: ELK, Grafana Loki, ou filas de eventos) para auditoria completa.

## 6. Instruções ao Code Agent

1. **Gerar três projetos** FastAPI (ou framework escolhido): `auth-service`, `admin-service`, `app-template`.
2. Cada projeto deve incluir:

   * `Dockerfile` e `docker-compose.yml` para rede interna.
   * `pytest` com testes de smoke para endpoints principais.
   * Configuração de variáveis de ambiente (sem segredos hardcoded).
3. **Implementar contratos** de API de observabilidade no `admin-service` e `app-template`.
4. **Configurar JWT** no `auth-service` e validação (local ou introspect) no `admin-service` e `app-template`.
5. **Documentar** cada projeto com OpenAPI/Swagger.

## 7. Esquema de Banco de Dados

### 7.1. auth-service

* Contém todas as tabelas de identidade e acesso:

  * `tenants` (com isolamento por aplicação)
  * `users` (usuários finais por tenant)
  * `admin_users` (admins globais, ex: você)

    * Recomenda-se que `admin_users` tenha um campo `role` ou `scope` para representar funções futuras como `admin`, `support`, `auditor`, mesmo que hoje só exista `superadmin`
  * `tokens` (gerenciamento de sessões e revogações)
* Contém todas as tabelas de identidade e acesso:

  * `tenants`
  * `users`
  * `admin_users`
  * `tokens`

### 7.2. admin-service

* Focado em controle e observabilidade. Não faz autenticação.
* Embora atualmente tenha apenas um usuário (você), recomenda-se desde já a introdução de um campo `role` (por exemplo: `superadmin`, `operator`, `auditor`) para facilitar possíveis expansões futuras.
* Tabelas sugeridas:
* Focado em controle e observabilidade. Não faz autenticação.
* Tabelas sugeridas:

#### `api_keys`

```sql
id UUID PK
name TEXT
key TEXT (hashed or UUID)
tenant_id UUID (opcional)
created_by TEXT
created_at TIMESTAMP
revoked_at TIMESTAMP (opcional)
```

#### `audit_logs`

```sql
id UUID PK
timestamp TIMESTAMP
actor TEXT (usuário, serviço ou "admin-service")
action TEXT
resource TEXT
details JSONB
```

#### `metrics_cache`

```sql
id UUID PK
service TEXT (ex: app1, app2, auth-service)
snapshot_time TIMESTAMP
stats JSONB (conforme schema de /admin/stats)
```

---

**Fim da especificação atualizada.**




**Finalizacao da conversa**

Excelente refinamento — agora chegamos no ponto-chave da **integração entre `admin-service` e `auth-service`** em relação à **gestão e uso das API Keys**.

---

## 🧭 Antes de decidir, vamos alinhar o que você quer garantir:

1. O `admin-service` é o **único responsável por gerar e revogar API Keys**.
2. O `auth-service` precisa validar essas chaves **rapidamente**, em todas as requisições externas.
3. Nenhuma chave deve ser armazenada em texto claro — apenas o **hash da chave** é propagado.
4. Você quer manter os serviços **desacoplados** e seguros.

---

## ✅ Opção recomendada:

> **`admin-service` salva apenas o hash da chave no banco dele e publica o hash no Redis.**
> O `auth-service` consome isso do Redis e **valida localmente**, podendo opcionalmente salvar/cachear no próprio banco.

---

### 🛠 Fluxo ideal (push-based)

```text
             Geração de chave (admin-service)
                           ↓
┌──────────────────────────────┐
│ admin-service                │
│                              │
│ 1. Gera chave: XYZ123        │
│ 2. Salva hash no próprio DB  │
│ 3. Publica hash+metadados → Redis
└──────────────────────────────┘
                           ↓
┌──────────────────────────────┐
│ auth-service                 │
│                              │
│ 4. Escuta/puxa do Redis      │
│ 5. Salva localmente (ex: SQLite, memória ou cache em banco)
│ 6. Toda requisição: compara hash local
└──────────────────────────────┘
```

---

## 📌 O que vai no Redis

Um objeto leve como:

```json
{
  "key_hash": "sha256(ABC123...)",
  "tenant_id": "app1",
  "created_at": "2025-07-13T12:00:00Z",
  "revoked": false
}
```

---

## ✅ Benefícios dessa abordagem

| Aspecto        | Vantagem                                                                                      |
| -------------- | --------------------------------------------------------------------------------------------- |
| Segurança      | `auth-service` **nunca vê** a API Key em texto claro                                          |
| Desempenho     | Validação é **local** e instantânea                                                           |
| Desacoplamento | Sem dependência de chamadas entre serviços                                                    |
| Simplicidade   | O Redis atua como canal de sincronização                                                      |
| Flexibilidade  | O `auth-service` pode salvar em banco local (PostgreSQL, SQLite, etc), se quiser persistência |

---

## ❓ Alternativa: Auth salva em sua tabela `tenants`?

Isso **só faz sentido se a API Key for uma propriedade do tenant diretamente** — ou seja, se você quiser garantir que:

* Cada tenant tenha **exatamente uma chave**
* E que essa chave seja parte do próprio registro do tenant

### Mas isso quebra um pouco a separação de papéis:

* O `auth-service` **não deveria ser responsável pela gestão das chaves**.
* Ele **deve apenas validar** se a chave recebida é uma das permitidas.

### Ou seja: ❌ **não é ideal que o `auth-service` salve a chave na tabela `tenants` diretamente**.

---

## ✅ Resumo final

| Papel                  | Serviço responsável                 |
| ---------------------- | ----------------------------------- |
| Gerar, revogar chave   | `admin-service`                     |
| Armazenar chave (hash) | `admin-service` + Redis             |
| Validar chave recebida | `auth-service`, via Redis/cache     |
| Ligar chave ao tenant  | via `tenant_id` no payload da chave |

---

