from fastapi import FastAPI, Depends, HTTPException
import redis.asyncio as redis
import os
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
import structlog
from datetime import datetime

from app.core.logging import configure_logging

configure_logging()

logger = structlog.get_logger(__name__)

app = FastAPI(
    title="Admin Service",
    description="Centralized control panel for Quanthea Corp ecosystem.",
    version="0.1.0",
)

start_time = datetime.now()

DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    logger.error("DATABASE_URL environment variable not set")
    raise ValueError("DATABASE_URL environment variable not set")

engine = create_async_engine(DATABASE_URL, echo=False) # Set echo to False for production
AsyncSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine, class_=AsyncSession)

async def get_redis():
    redis_url = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    try:
        r = await redis.from_url(redis_url, decode_responses=True)
        yield r
    finally:
        await r.close()

@app.get("/health", status_code=200, tags=["Health"])
async def health_check(r: redis.Redis = Depends(get_redis)):
    """Simple health check endpoint."""
    uptime_seconds = int((datetime.now() - start_time).total_seconds())
    redis_status_val = "connected"
    db_status_val = "connected"

    try:
        await r.ping()
        logger.info("Redis connection successful")
    except Exception:
        redis_status_val = "disconnected"
        logger.error("Redis connection failed")

    try:
        async with AsyncSessionLocal() as session:
            await session.execute(text("SELECT 1"))
        logger.info("Database connection successful")
    except Exception:
        db_status_val = "disconnected"
        logger.error("Database connection failed")

    return {"status": "ok", "uptime_seconds": uptime_seconds, "db_status": db_status_val, "redis_status": redis_status_val, "version": app.version}
