# Database (admin-service dedicated)
DATABASE_URL=postgresql+asyncpg://admin:pass@admin-service-db:5432/admin_service

# Redis (shared with auth-service)
REDIS_URL=redis://quanthea-corp-redis:6379/0

# Integration with auth-service
AUTH_SERVICE_URL=http://localhost:8002
AUTH_SERVICE_INTERNAL_KEY=shared-secret-key  # For internal validation

# Server
ADMIN_SERVICE_PORT=8000
LOG_LEVEL=INFO

# CORS
CORS_ORIGINS=["http://localhost:5173"]
