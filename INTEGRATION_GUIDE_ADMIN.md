# Admin Service Integration Guide

**Integration guide for admin-service frontend with auth-service backend**

This guide details how the admin-service frontend integrates with the auth-service for authentication and tenant management, following the Quanthea Corp ecosystem architecture.

## 1. Architecture Overview

The admin-service operates within the Quanthea Corp ecosystem with clear separation of responsibilities:

- **auth-service**: Handles authentication, user management, and API key validation
- **admin-service**: Manages tenants, generates API keys, and provides observability dashboard
- **Redis**: Shared cache for push-based API key synchronization

## 2. Environment Configuration

### Base URLs

Configure the following environment variables:

**Development:**

```env
VITE_AUTH_SERVICE_URL=http://localhost:8002
VITE_ADMIN_SERVICE_URL=http://localhost:8003  # When backend ready
```

**Production:**

```env
VITE_AUTH_SERVICE_URL=https://quanthea-auth-service.onrender.com
VITE_ADMIN_SERVICE_URL=https://quanthea-admin-service.onrender.com
```

## 3. Authentication Flow (auth-service)

Admin authentication is handled by the auth-service. The admin-service frontend authenticates against auth-service endpoints.

### Admin Login

**Endpoint:** `POST {AUTH_SERVICE_URL}/api/v1/admin/login`

**Description:** Authenticates an admin user with email and password.

**Request:**

```http
POST /api/v1/admin/login
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=your_secure_password
```

**Success Response:**

```json
{
  "access_token": "eyJhbGciOiJIUzI1Ni...",
  "token_type": "bearer"
}
```

**Error Response:**

```json
{
  "detail": "Incorrect email or password"
}
```

### Token Management (LCL-36 ✅)

**IMPORTANT:** The admin-service implements secure token management:

- **Storage**: Uses sessionStorage (not localStorage) for XSS protection
- **Expiration**: 30-minute automatic expiration with cleanup
- **Auto-logout**: Automatic session cleanup on token expiration
- **Implementation**: Centralized Token Manager (`src/utils/tokenManager.ts`)

**Usage Example:**

```javascript
import tokenManager from "../utils/tokenManager";

// After successful login
const { access_token } = response.data;
tokenManager.setToken(access_token, 30); // 30 minutes

// Token is automatically attached to requests via Axios interceptor
// No manual Authorization header needed
```

**Authentication Headers:**
All authenticated requests automatically include:

```http
Authorization: Bearer {token}
```

## 4. CORS Configuration

The auth-service implements dynamic CORS policy:

- **Development**: All origins (`*`) allowed for development ease
- **Production**: Only configured origins in environment variables:
  - `CORS_ORIGINS`: For client applications
  - `ADMIN_FRONTEND_URL`: For admin-service frontend

**Required Configuration:**
Ensure your admin frontend URL is properly configured:

- **Development**: `http://localhost:5173`
- **Production**: Your deployed frontend URL

**Environment Variable:**

```env
ADMIN_FRONTEND_URL=https://your-admin-frontend.com
```

## 5. Current Integration (auth-service endpoints)

**Note:** These endpoints are currently available in auth-service. When admin-service backend is implemented (LCL-48), tenant and API key management will move to admin-service.

All endpoints require JWT authentication with `Authorization: Bearer <token>` header.

### 5.1. List Tenants

**Endpoint:** `GET {AUTH_SERVICE_URL}/api/v1/admin/tenants`

**Description:** Lists all registered tenants with API key status (without exposing the actual keys).

**Response:**

```json
[
  {
    "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
    "name": "Web Application",
    "description": "Main company frontend",
    "is_active": true,
    "created_at": "2023-10-26T10:00:00.000Z",
    "updated_at": "2023-10-26T10:00:00.000Z",
    "has_api_key": true
  },
  {
    "id": "b2c3d4e5-f6a7-8901-2345-67890abcdef0",
    "name": "Mobile App",
    "description": "iOS and Android app",
    "is_active": true,
    "created_at": "2023-10-27T11:00:00.000Z",
    "updated_at": "2023-10-27T11:00:00.000Z",
    "has_api_key": false
  }
]
```

### 5.2. Create/Update API Key

**Endpoint:** `POST {AUTH_SERVICE_URL}/api/v1/admin/tenants/{tenant_id}/api-key`

**Description:** Creates or updates an API key for a tenant. If tenant already has an API key, it will be replaced.

**Path Parameters:**

- `tenant_id`: UUID of the tenant

**Request Body:**

```json
{
  "api_key": "your_new_secret_api_key_here"
}
```

**Response:**

```json
{
  "message": "API Key for tenant a1b2c3d4-e5f6-7890-1234-567890abcdef created/updated successfully"
}
```

### 5.3. Update API Key (PUT)

**Endpoint:** `PUT {AUTH_SERVICE_URL}/api/v1/admin/tenants/{tenant_id}/api-key`

**Description:** Updates the API key for an existing tenant. Functionally similar to POST above.

**Path Parameters:**

- `tenant_id`: UUID of the tenant

**Request Body:**

```json
{
  "api_key": "another_secret_api_key_here"
}
```

**Response:**

```json
{
  "message": "API Key for tenant a1b2c3d4-e5f6-7890-1234-567890abcdef updated successfully"
}
```

### 5.4. Revoke API Key

**Endpoint:** `DELETE {AUTH_SERVICE_URL}/api/v1/admin/tenants/{tenant_id}/api-key`

**Description:** Removes the API key from a tenant, effectively revoking their API access.

**Path Parameters:**

- `tenant_id`: UUID of the tenant

**Response:**

```json
{
  "message": "API Key for tenant a1b2c3d4-e5f6-7890-1234-567890abcdef revoked successfully"
}
```

## 6. Error Handling

The service returns standard HTTP status codes:

- **`400 Bad Request`**: Malformed request or invalid data
- **`401 Unauthorized`**: Missing or invalid authentication credentials
- **`403 Forbidden`**: Valid credentials but insufficient permissions
- **`404 Not Found`**: Resource not found (e.g., invalid tenant_id)
- **`500 Internal Server Error`**: Unexpected server error

Always check the `detail` field in JSON responses for specific error information.

**Error Response Format:**

```json
{
  "detail": "Specific error message"
}
```

## 7. Logout

There is no server-side logout endpoint. To logout an admin user, the frontend uses the Token Manager:

```javascript
import tokenManager from "../utils/tokenManager";

// Secure logout
tokenManager.clearAll(); // Clears all token data
```

The token will automatically become invalid after expiration (30 minutes).

## 8. Future Architecture (LCL-48+)

When the admin-service backend is implemented, the architecture will change:

### Current (Temporary)

```
Frontend → auth-service (admin endpoints)
```

### Future (Target Architecture)

```
Frontend → admin-service → auth-service (authentication only)
                      ↓
                    Redis (API key sync)
```

### Migration Plan

1. **LCL-48**: Implement admin-service backend
2. **LCL-50**: Move API key management to admin-service
3. **LCL-53**: Add observability endpoints
4. Update this integration guide accordingly

## 9. Development Notes

- **Token Security**: Implemented via Token Manager (LCL-36 ✅)
- **Environment Variables**: Use VITE\_ prefix for frontend variables
- **CORS**: Ensure proper configuration for your deployment URLs
- **Error Handling**: Implement proper error boundaries and user feedback

---

**Last Updated:** 2025-07-14
**Status:** Current integration with auth-service (temporary)
**Next:** Migration to admin-service backend (LCL-48)
