# Admin Service Backend - Implementation Plan

**LCL-48: Backend MVP (FastAPI Application Setup)**

This document outlines the complete implementation plan for the admin-service backend, following Quanthea Corp ecosystem guidelines and architecture specifications.

## 📋 Core Principles (QUANTHEACORP.md Compliance)

### ✅ What admin-service DOES:

- **API Key Management**: Sole responsibility for generating/revoking API Keys
- **Push-based Redis**: Publishes API key hashes to Redis for auth-service consumption
- **Observability**: Collects metrics (`/admin/stats`, `/admin/health`, `/admin/logs`)
- **Tenant Monitoring**: Visualizes and monitors tenants (read-only from auth-service)
- **Control Panel**: Backend for administrative dashboard

### ❌ What admin-service DOES NOT DO:

- **Authentication**: No JWT generation, only validates tokens from auth-service
- **User Management**: No user CRUD operations
- **Tenant Storage**: Tenants are stored in auth-service, admin-service only caches/views
- **Password Handling**: No login/logout endpoints

## 🛠️ Technology Stack

### Core Framework

- **FastAPI** - Async web framework with automatic OpenAPI
- **Pydantic v2** - Data validation and serialization
- **Python 3.13** - Latest Python version

### Database & ORM

- **PostgreSQL** - Dedicated database for admin-service
- **SQLAlchemy 2.0** - Async ORM
- **Alembic** - Database migrations
- **psycopg[binary]** - PostgreSQL driver (psycopg3)

### Cache & Integration

- **Redis** - Shared with auth-service for push-based API key sync
- **redis-py** - Async Redis client
- **httpx** - HTTP client for auth-service communication

### Security & Crypto

- **argon2-cffi** - Password hashing (when needed)
- **cryptography** - Cryptographic utilities
- **secrets** - Secure API key generation

### Development & Testing

- **pytest** - Testing framework
- **pytest-asyncio** - Async testing support
- **httpx** - Test client
- **pytest-cov** - Code coverage

### Deployment

- **Docker** - Containerization with python:3.13-slim
- **uvicorn** - ASGI server

## 📁 Project Structure

```
admin-service/
├── backend/                    # Backend FastAPI
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI app entry point
│   │   ├── core/              # Core configuration
│   │   │   ├── __init__.py
│   │   │   ├── config.py      # Settings & environment
│   │   │   ├── redis.py       # Redis connection & publishing
│   │   │   ├── database.py    # Database connection
│   │   │   └── auth.py        # JWT validation (consume auth-service tokens)
│   │   ├── api/               # API routes
│   │   │   ├── __init__.py
│   │   │   ├── deps.py        # Dependencies (JWT validation, db)
│   │   │   └── v1/            # API v1
│   │   │       ├── __init__.py
│   │   │       ├── api.py     # Router aggregator
│   │   │       └── endpoints/
│   │   │           ├── __init__.py
│   │   │           ├── tenants.py       # Tenant management (read-only)
│   │   │           ├── api_keys.py      # API key generation/revocation
│   │   │           ├── admin.py         # /admin/stats, /health, /logs
│   │   │           └── observability.py # Metrics collection
│   │   ├── models/            # SQLAlchemy models
│   │   │   ├── __init__.py
│   │   │   ├── base.py        # Base model class
│   │   │   ├── tenant.py      # Tenant model (cache/view from auth-service)
│   │   │   ├── api_key.py     # API Key model (admin-service owns)
│   │   │   ├── audit_log.py   # Audit logging
│   │   │   └── metrics_cache.py # Metrics cache
│   │   ├── schemas/           # Pydantic schemas
│   │   │   ├── __init__.py
│   │   │   ├── tenant.py      # Tenant schemas
│   │   │   ├── api_key.py     # API key schemas
│   │   │   ├── admin.py       # Admin/observability schemas
│   │   │   └── common.py      # Common schemas
│   │   ├── services/          # Business logic
│   │   │   ├── __init__.py
│   │   │   ├── tenant_service.py        # Tenant operations (read from auth-service)
│   │   │   ├── api_key_service.py       # API key generation + Redis publish
│   │   │   ├── auth_service_client.py   # Client for auth-service
│   │   │   └── observability_service.py # Metrics collection
│   │   ├── utils/             # Utilities
│   │   │   ├── __init__.py
│   │   │   ├── redis_publisher.py   # Redis publishing (push-based)
│   │   │   ├── crypto.py            # API key generation, hashing
│   │   │   └── logging.py           # Logging setup
│   │   └── tests/             # Tests
│   │       ├── __init__.py
│   │       ├── conftest.py    # Pytest configuration
│   │       ├── test_tenants.py
│   │       ├── test_api_keys.py
│   │       └── test_observability.py
│   ├── alembic/               # Database migrations
│   │   ├── versions/
│   │   ├── env.py
│   │   └── alembic.ini
│   ├── requirements.txt       # Python dependencies (NO VERSIONS)
│   ├── requirements-dev.txt   # Development dependencies
│   ├── .gitignore            # Backend gitignore
│   ├── Dockerfile            # python:3.13-slim
│   ├── .env.example          # Environment template
│   └── pyproject.toml        # Project configuration
├── docker-compose.yml        # Local development
└── frontend/                 # Frontend existente
    └── ... (já implementado)
```

## 🗄️ Database Models (admin-service DB)

### API Keys (admin-service owns)

```sql
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT,                          -- Descriptive name
    key_hash TEXT NOT NULL,             -- SHA-256 hash of the key
    tenant_id UUID,                     -- Reference to tenant (from auth-service)
    created_by TEXT,                    -- Admin who created
    created_at TIMESTAMP DEFAULT NOW(),
    revoked_at TIMESTAMP,               -- NULL = active
    revoked BOOLEAN DEFAULT FALSE
);
```

### Audit Logs

```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    timestamp TIMESTAMP DEFAULT NOW(),
    actor TEXT,                         -- Admin user or "system"
    action TEXT,                        -- "create_api_key", "revoke_api_key"
    resource TEXT,                      -- "api_key", "tenant"
    details JSONB                       -- Additional data
);
```

### Metrics Cache

```sql
CREATE TABLE metrics_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service TEXT,                       -- "auth-service", "app1", etc
    snapshot_time TIMESTAMP DEFAULT NOW(),
    stats JSONB                         -- Data from /admin/stats
);
```

## ⚙️ Environment Configuration

```env
# Database (admin-service dedicated)
DATABASE_URL=postgresql+asyncpg://admin:pass@localhost:5433/admin_service

# Redis (shared with auth-service)
REDIS_URL=redis://localhost:6379/0

# Integration with auth-service
AUTH_SERVICE_URL=http://localhost:8002
AUTH_SERVICE_INTERNAL_KEY=shared-secret-key  # For internal validation

# Server
ADMIN_SERVICE_PORT=8003
LOG_LEVEL=INFO

# CORS
CORS_ORIGINS=["http://localhost:5173"]
```

## 📦 Dependencies (requirements.txt) - NO VERSIONS

```txt
# Core
fastapi
uvicorn[standard]
pydantic
pydantic-settings

# Database
sqlalchemy
alembic
psycopg[binary]

# Cache & Integration
redis
httpx

# Security & Crypto
argon2-cffi
cryptography

# Utilities
python-dotenv
structlog
```

## 🐳 Docker Configuration

### Dockerfile

```dockerfile
FROM python:3.13-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm-rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application
COPY app/ ./app/
COPY alembic/ ./alembic/
COPY alembic.ini .

# Run migrations and start app
CMD ["sh", "-c", "alembic upgrade head && uvicorn app.main:app --host 0.0.0.0 --port 8003"]
```

### .gitignore (backend)

```gitignore
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Database
*.sqlite
*.db

# Environment
.env
.env.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# Testing
.pytest_cache/
.coverage
htmlcov/

# Logs
*.log
logs/

# Docker
.dockerignore

# Alembic
alembic/versions/*.py
!alembic/versions/__init__.py
```

## 🎯 API Endpoints (NO AUTHENTICATION)

### Tenants (LCL-49) - Read-only from auth-service

```python
GET /api/v1/tenants           # List tenants (via auth-service)
GET /api/v1/tenants/{id}      # Get tenant (via auth-service)
# Note: Tenant CRUD remains in auth-service
```

### API Keys (LCL-50) - admin-service owns

```python
POST   /api/v1/tenants/{id}/api-keys        # Generate API key + publish Redis
GET    /api/v1/tenants/{id}/api-keys        # List tenant API keys
DELETE /api/v1/tenants/{id}/api-keys/{key_id}  # Revoke + remove from Redis
```

### Observability (LCL-53)

```python
GET /api/v1/admin/stats       # Aggregated metrics
GET /api/v1/admin/health      # Health check
GET /api/v1/admin/logs        # Audit logs
```

## 🔄 Push-Based Flow (API Keys)

### API Key Generation Flow

```python
# 1. admin-service generates API key
api_key = secrets.token_urlsafe(32)
key_hash = hashlib.sha256(api_key.encode()).hexdigest()

# 2. Save to own database
await db.execute(
    "INSERT INTO api_keys (key_hash, tenant_id, ...) VALUES (...)"
)

# 3. Publish to Redis for auth-service
redis_data = {
    "key_hash": key_hash,
    "tenant_id": tenant_id,
    "created_at": datetime.utcnow().isoformat(),
    "revoked": False
}
await redis.set(f"api_key:{key_hash}", json.dumps(redis_data))
```

### API Key Revocation Flow

```python
# 1. Mark as revoked in admin-service DB
await db.execute(
    "UPDATE api_keys SET revoked = TRUE, revoked_at = NOW() WHERE id = ?"
)

# 2. Remove from Redis
await redis.delete(f"api_key:{key_hash}")

# 3. Log audit event
await audit_log("revoke_api_key", {"key_id": key_id, "tenant_id": tenant_id})
```

## 🚫 What NOT to Implement

- ❌ **Own Authentication** (JWT validation only)
- ❌ **Tenant CRUD** (remains in auth-service)
- ❌ **User CRUD** (remains in auth-service)
- ❌ **python-jose** (no JWT generation needed)
- ❌ **Password Storage** (no authentication)
- ❌ **Login/Logout endpoints** (handled by auth-service)

## ✅ Implementation Checklist

### Phase 1: LCL-48 - Backend MVP

- [ ] Project structure setup
- [ ] FastAPI app base configuration
- [ ] Database setup (PostgreSQL)
- [ ] Redis connection
- [ ] Docker configuration
- [ ] Health check endpoint
- [ ] Basic logging setup

### Phase 2: LCL-49 - Tenant Management

- [ ] HTTP client for auth-service
- [ ] Read-only tenant endpoints
- [ ] Tenant data caching
- [ ] Error handling for auth-service communication

### Phase 3: LCL-50 - API Key Management

- [ ] Secure API key generation
- [ ] Hashing and storage
- [ ] Redis publishing (push-based)
- [ ] Revocation and cleanup
- [ ] Audit logging

### Phase 4: LCL-53 - Observability

- [ ] Metrics collection
- [ ] Audit log endpoints
- [ ] Health check implementation
- [ ] Dashboard data endpoints
- [ ] Performance monitoring

## 🔗 Integration Points

### With auth-service

- **Tenant Data**: Read-only access via HTTP API
- **JWT Validation**: Validate tokens issued by auth-service
- **Redis Sync**: Publish API key hashes for validation

### With Frontend

- **CORS Configuration**: Allow frontend origin
- **API Responses**: JSON format compatible with existing frontend
- **Error Handling**: Consistent error response format

## 📊 Success Criteria

### Technical Requirements

- [ ] FastAPI app running on port 8003
- [ ] PostgreSQL database connected and migrated
- [ ] Redis connection established
- [ ] Docker container builds and runs
- [ ] All endpoints return proper HTTP status codes
- [ ] API key generation and Redis publishing working
- [ ] Health check endpoint returns service status

### Integration Requirements

- [ ] Frontend can connect to backend
- [ ] auth-service integration working
- [ ] Redis push-based sync operational
- [ ] CORS properly configured
- [ ] Error responses properly formatted

### Security Requirements

- [ ] No authentication implementation (as per guidelines)
- [ ] API keys properly hashed before storage
- [ ] Redis data properly formatted
- [ ] Audit logging for all operations
- [ ] No sensitive data in logs

---

**This implementation plan follows QUANTHEACORP.md guidelines and addresses the specific requirements for admin-service backend development.**

**Key Compliance Points:**

1. ✅ No authentication (only JWT validation)
2. ✅ Argon2 for hashing (when needed)
3. ✅ psycopg[binary] (psycopg3)
4. ✅ Python 3.13-slim
5. ✅ Requirements without versions
6. ✅ Proper .gitignore
7. ✅ Focus on API Keys and observability
8. ✅ Push-based Redis architecture

**Ready for implementation approval and execution.**
