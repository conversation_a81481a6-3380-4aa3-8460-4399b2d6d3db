# 🏢 Quanthea Corp - Admin Service

**Administrative Dashboard and Observability Panel for Quanthea Corp Ecosystem**

The admin-service is the centralized control panel for managing tenants, API keys, and monitoring the entire Quanthea Corp application ecosystem. Built with React, TypeScript, and modern security practices.

## 🚀 Features

- **🔐 Secure Authentication**: JWT-based admin authentication with secure token management
- **🏢 Tenant Management**: Complete CRUD operations for tenant administration
- **🔑 API Key Management**: Generate, update, and revoke API keys for tenants
- **📊 Dashboard**: Real-time metrics and system observability
- **🛡️ Security**: XSS-resistant token storage with automatic expiration
- **📱 Responsive UI**: Modern interface built with Tailwind CSS

## 🛠️ Tech Stack

- **Frontend**: React 18 + TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Routing**: React Router DOM
- **HTTP Client**: Axios
- **State Management**: React Hooks + Context API

## 🔧 Installation & Setup

### Prerequisites

- Node.js 18+
- npm or yarn

### Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd admin_service

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

### Environment Configuration

Create a `.env.local` file:

```env
VITE_API_BASE_URL=http://localhost:8002
```

## 🏗️ Project Structure

```
src/
├── api/                 # API configuration and interceptors
│   └── axios.ts        # Axios setup with token management
├── components/         # Reusable UI components
│   ├── Header.tsx
│   ├── Sidebar.tsx
│   ├── ProtectedRoute.tsx
│   └── dashboard/
├── context/           # React Context providers
│   └── NotificationContext.tsx
├── pages/             # Page components
│   ├── LoginPage.tsx
│   ├── TenantsPage.tsx
│   ├── AdminDashboardPage.tsx
│   └── HomePage.tsx
├── utils/             # Utility functions
│   └── tokenManager.ts # Secure token management
└── App.tsx            # Main application component
```

## 🔐 Security Features

### Token Manager (LCL-36 ✅)

- **Secure Storage**: Migration from localStorage to sessionStorage
- **Auto-Expiration**: 30-minute token validity with automatic cleanup
- **XSS Protection**: Mitigated XSS vulnerabilities
- **Auto-Logout**: Automatic session cleanup on token expiration
- **Legacy Migration**: Transparent migration of old tokens

### Authentication Flow

1. Admin login with email/password
2. JWT token received and securely stored
3. Token automatically attached to API requests
4. Auto-logout on token expiration or 401 responses

## 📊 Available Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
```

## 🔗 Integration

### Backend Requirements

- **auth-service**: Provides authentication endpoints
- **API Base URL**: Configurable via environment variables
- **CORS**: Must allow frontend origin

### API Endpoints Used

```
POST /api/v1/admin/login              # Admin authentication
GET  /api/v1/admin/tenants            # List tenants
POST /api/v1/admin/tenants/{id}/api-key    # Generate API key
PUT  /api/v1/admin/tenants/{id}/api-key    # Update API key
DELETE /api/v1/admin/tenants/{id}/api-key  # Revoke API key
```

## 🚧 Development Status

### ✅ Completed Features

- Frontend React application with TypeScript
- Secure authentication system
- Tenant management interface
- API key management
- Responsive dashboard
- Security improvements (LCL-36)

### 🔄 In Progress

- Backend FastAPI integration (LCL-48)
- Real-time metrics dashboard
- Advanced observability features

### 📋 Planned Features

- HTTP-only cookie authentication
- Multi-factor authentication
- Advanced tenant analytics
- Real-time notifications

## 🤝 Contributing

1. Follow TypeScript strict mode
2. Use semantic commit messages
3. Maintain test coverage
4. Follow security best practices

## 📝 License

Private - Quanthea Corp Internal Use Only
